{"name": "@my/config", "version": "0.0.1", "sideEffects": ["*.css"], "private": true, "types": "./src", "main": "src/index.ts", "files": ["types", "dist"], "scripts": {"build": "tamagui-build --skip-types", "watch": "tamagui-build --skip-types --watch"}, "dependencies": {"@tamagui/animations-react-native": "^1.132.10", "@tamagui/font-inter": "^1.132.10", "@tamagui/shorthands": "^1.132.10", "@tamagui/themes": "^1.132.10", "tamagui": "^1.132.10"}, "devDependencies": {"@tamagui/build": "^1.132.10"}}