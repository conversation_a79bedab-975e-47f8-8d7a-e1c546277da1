/**
 * Desktop React Native App with Tamagui + Solito
 * Integrated with monorepo shared packages
 */

import React from 'react';
import { useColorScheme } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Provider } from 'app/provider';
import { HomeScreen } from 'app/features/home/<USER>';
import { UserDetailScreen } from 'app/features/user/detail-screen';

const Stack = createNativeStackNavigator();

function Navigation() {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Home">
        <Stack.Screen
          name="Home"
          component={HomeScreenWrapper}
          options={{ title: 'Home' }}
        />
        <Stack.Screen
          name="User"
          component={UserDetailScreenWrapper}
          options={{
            title: 'User',
            presentation: 'modal',
            animation: 'slide_from_right',
            gestureEnabled: true,
            gestureDirection: 'horizontal',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

function HomeScreenWrapper() {
  return <HomeScreen />;
}

function UserDetailScreenWrapper({ route }: { route: any }) {
  const { id } = route.params || { id: 'default' };
  return <UserDetailScreen id={id} />;
}

export default function App(): React.JSX.Element {
  const colorScheme = useColorScheme();
  const theme = colorScheme === 'dark' ? 'dark' : 'light';

  return (
    <SafeAreaProvider>
      <Provider defaultTheme={theme}>
        <Navigation />
      </Provider>
    </SafeAreaProvider>
  );
}
