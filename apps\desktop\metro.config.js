const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const path = require('node:path');

/**
 * Metro configuration for monorepo support
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */

const projectRoot = __dirname;
const workspaceRoot = path.resolve(projectRoot, '../..');

const config = {
  // 1. Watch all files within the monorepo
  watchFolders: [workspaceRoot],

  // 2. Let Metro know where to resolve packages and in what order
  resolver: {
    nodeModulesPaths: [
      path.resolve(projectRoot, 'node_modules'),
      path.resolve(workspaceRoot, 'node_modules'),
    ],
    // 3. Force Metro to resolve (sub)dependencies only from the `nodeModulesPaths`
    disableHierarchicalLookup: true,
  },

  // 4. Enable support for require.context (needed for some packages)
  transformer: {
    unstable_allowRequireContext: true,
    minifierPath: require.resolve('metro-minify-terser'),
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
