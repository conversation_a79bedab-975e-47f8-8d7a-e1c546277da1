import { useState } from 'react'
import { Button, useIsomorphicLayoutEffect } from 'tamagui'
import { Platform } from 'react-native'

// Web-specific imports (only loaded on web)
let useThemeSetting: any
let useRootTheme: any

if (Platform.OS === 'web') {
  try {
    const nextTheme = require('@tamagui/next-theme')
    useThemeSetting = nextTheme.useThemeSetting
    useRootTheme = nextTheme.useRootTheme
  } catch (e) {
    // Fallback if next-theme is not available
    useThemeSetting = () => ({ current: 'light', toggle: () => {} })
    useRootTheme = () => ['light']
  }
} else {
  // React Native fallback
  useThemeSetting = () => ({ current: 'light', toggle: () => {} })
  useRootTheme = () => ['light']
}

export const SwitchThemeButton = () => {
  const themeSetting = useThemeSetting()
  const [theme] = useRootTheme()

  const [clientTheme, setClientTheme] = useState<string | undefined>('light')

  useIsomorphicLayoutEffect(() => {
    setClientTheme(themeSetting.forcedTheme || themeSetting.current || theme)
  }, [themeSetting.current, themeSetting.resolvedTheme])

  return <Button onPress={themeSetting.toggle}>Change theme: {clientTheme}</Button>
}
