{"version": "0.0.0", "name": "app", "main": "index.ts", "private": true, "sideEffects": ["*.css"], "dependencies": {"@my/ui": "0.0.1", "@react-navigation/native": "^6.1.17", "@tamagui/animations-react-native": "^1.132.10", "@tamagui/colors": "^1.132.10", "@tamagui/font-inter": "^1.132.10", "@tamagui/lucide-icons": "^1.132.10", "@tamagui/shorthands": "^1.132.10", "@tamagui/themes": "^1.132.10", "burnt": "^0.12.2", "expo-constants": "~17.1.6", "expo-linking": "~7.1.4", "react-native-safe-area-context": "5.4.0", "solito": "^4.2.2"}, "devDependencies": {"@types/react": "~19.0.10", "@types/react-native": "^0.73.0"}}