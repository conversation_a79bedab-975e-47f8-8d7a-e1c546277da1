{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@my/ui": ["../../packages/ui/src"], "@my/ui/*": ["../../packages/ui/src/*"], "@my/config": ["../../packages/config/src"], "@my/config/*": ["../../packages/config/src/*"], "app/*": ["../../packages/app/*"]}, "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true}, "include": ["**/*.ts", "**/*.tsx", "../../packages/app/**/*.ts", "../../packages/app/**/*.tsx", "../../packages/ui/src/**/*.ts", "../../packages/ui/src/**/*.tsx", "../../packages/config/src/**/*.ts"], "exclude": ["node_modules", "../../packages/*/node_modules"]}