### **Project Nexus: The Master Development Roadmap**

**Guiding Principles:**
*   **Backend:** A scalable, secure, and decoupled microservices architecture using Domain-Driven Design (DDD) principles.
*   **Technology:** State-of-the-art, stable tooling to ensure performance, developer experience, and long-term maintainability.
*   **Automation:** A "Paved Road" approach with robust CI/CD from day one to ensure quality and consistency.

---
---

### **Roadmap Part 1: Backend Foundation (The Engine Room)**

**Objective:** To establish a containerized, multi-service backend environment with a functional API Gateway, database connectivity, and a CI pipeline, ready for business logic development.

**Projected Tech Stack (as of July 20, 2025):**
*   **Framework:** NestJS v12.x
*   **Containerization:** Docker & Docker Compose v2.x
*   **Package Manager:** PNPM v9.x
*   **Node.js:** LTS v24.x
*   **Database:** PostgreSQL v17
*   **Monorepo Tooling:** PNPM Workspaces

---

#### **Phase 0: Environment & Tooling Setup**
*Goal: Prepare a developer's machine with all necessary backend tools.*

*   [ ] **Step 1: Install Core Tools:**
    *   Install a Node.js version manager (e.g., `nvm` or `fnm`) and use it to install Node.js LTS: `nvm install --lts && nvm use --lts`.
    *   Enable `corepack` to manage the package manager version and install PNPM: `corepack enable && corepack prepare pnpm@latest --activate`.
    *   Install the NestJS CLI globally: `pnpm add -g @nestjs/cli`.

*   [ ] **Step 2: Install Docker:**
    *   Install and run Docker Desktop for your operating system (Mac, Windows, or Linux). This provides the `docker` and `docker-compose` CLIs.

#### **Phase 1: Monorepo & Service Scaffolding**
*Goal: Create the backend monorepo structure and scaffold the initial microservices.*

*   [ ] **Step 1: Initialize the Monorepo:**
    *   Create the project directory: `mkdir erp-nexus-backend && cd erp-nexus-backend`.
    *   Create a root `package.json`: `pnpm init`.
    *   Create the workspace configuration file `pnpm-workspace.yaml` and add the following to it, telling PNPM where to find the services:
        ```yaml
        packages:
          - 'services/*'
        ```

*   [ ] **Step 2: Scaffold the Initial Microservices:**
    *   Create the directory for your services: `mkdir services`.
    *   Use the NestJS CLI to scaffold each service within the `services` directory:
        ```bash
        nest new services/identity-service --package-manager pnpm
        nest new services/tenant-service --package-manager pnpm
        nest new services/api-gateway --package-manager pnpm
        ```

#### **Phase 2: Containerization & Inter-Service Communication**
*Goal: Get all services and infrastructure running together in Docker and confirm they can communicate.*

*   [ ] **Step 1: Create Dockerfiles for Each Service:**
    *   For each service (e.g., in `services/identity-service/`), create a `Dockerfile`. Use a multi-stage build for optimized production images:
        ```Dockerfile
        # Stage 1: Build the application
        FROM node:24-alpine AS builder
        WORKDIR /usr/src/app
        COPY package.json pnpm-lock.yaml ./
        RUN pnpm install --frozen-lockfile
        COPY . .
        RUN pnpm build

        # Stage 2: Create the production image
        FROM node:24-alpine
        WORKDIR /usr/src/app
        COPY --from=builder /usr/src/app/node_modules ./node_modules
        COPY --from=builder /usr/src/app/dist ./dist
        CMD ["node", "dist/main"]
        ```

*   [ ] **Step 2: Create the Master Docker Compose File:**
    *   At the root of the backend monorepo, create a `docker-compose.yml` file. This is the master control file for your entire backend stack.
        ```yaml
        version: '3.9'

        services:
          # --- Application Services ---
          api-gateway:
            build: ./services/api-gateway
            ports:
              - "4000:3000" # Expose gateway to host at port 4000
            environment:
              - IDENTITY_SERVICE_URL=http://identity-service:3000
              - TENANT_SERVICE_URL=http://tenant-service:3000
            depends_on:
              - identity-service
              - tenant-service

          identity-service:
            build: ./services/identity-service
            environment:
              - DATABASE_URL=****************************************/identity_db?schema=public
            depends_on:
              - postgres

          tenant-service:
            build: ./services/tenant-service
            # Add environment and depends_on as needed

          # --- Infrastructure ---
          postgres:
            image: postgres:17-alpine
            environment:
              POSTGRES_USER: user
              POSTGRES_PASSWORD: password
              POSTGRES_DB: identity_db
            ports:
              - "5432:5432"
            volumes:
              - postgres_data:/var/lib/postgresql/data

        volumes:
          postgres_data:
        ```

*   [ ] **Step 3: Run the Stack & Verify Communication:**
    *   From the monorepo root, run the entire stack: `docker-compose up --build`.
    *   **Verification:** Add a `/health` endpoint to `identity-service`. In `api-gateway`, use NestJS's `HttpModule` or `Proxy` to forward requests from `/api/identity/health` to the `identity-service` URL defined in the environment. Test by calling `http://localhost:4000/api/identity/health` from your browser or Postman. A `200 OK` response confirms the network is working.

#### **Phase 3: Database Integration & The "Paved Road"**
*Goal: Connect a service to the database and establish a CI pipeline for quality assurance.*

*   [ ] **Step 1: Implement Database Connection & ORM:**
    *   In `identity-service`, install TypeORM and PostgreSQL driver: `pnpm --filter identity-service add @nestjs/typeorm typeorm pg`.
    *   In `identity-service/src/app.module.ts`, configure the `TypeOrmModule` to connect to the database using the `DATABASE_URL` environment variable.
    *   Create a `User` entity using TypeORM decorators. Use an ORM like Prisma or TypeORM to manage your schema.
    *   **Verification:** Modify the `/health` endpoint to run a simple query (e.g., `SELECT 1`) to confirm the database connection is live.

*   [ ] **Step 2: Establish the CI Pipeline:**
    *   Create a `.github/workflows/backend-ci.yml` file.
    *   Configure a GitHub Action to run on every push/pull request:
        1.  Checkout code.
        2.  Set up Docker Buildx.
        3.  Run `docker-compose build` for all services. This ensures the services are always buildable.
        4.  (Optional but recommended) Run linters and unit tests for each service.

---
---